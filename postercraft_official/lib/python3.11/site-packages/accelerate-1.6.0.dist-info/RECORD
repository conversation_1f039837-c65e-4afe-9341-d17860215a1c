../../../bin/accelerate,sha256=TKshJOGyI6xqAhOm7adBh9peNWC2GCCZhNMH1cvRBmE,275
../../../bin/accelerate-config,sha256=BXSRvgtCKXGEqjjc3XA88jY8u5GPPfMhhCI7yxKe9Qg,267
../../../bin/accelerate-estimate-memory,sha256=WpViAfNKbBBxp1ZNTns3uThu4RXpNMRh11M7tQjIDrk,269
../../../bin/accelerate-launch,sha256=P7s2Y0Fqqz88J2wV3-Tekhk0VX2VE7ao9397mDSdyLQ,267
../../../bin/accelerate-merge-weights,sha256=jzhfva76CMtPKSclNk1UwWHPpOymLKZ3Av6ehsjkutE,266
accelerate-1.6.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
accelerate-1.6.0.dist-info/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
accelerate-1.6.0.dist-info/METADATA,sha256=zT5ADQHZZeLT4qEiGMNSG4cT7hCnQplwyshDyeDyZNo,19421
accelerate-1.6.0.dist-info/RECORD,,
accelerate-1.6.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate-1.6.0.dist-info/WHEEL,sha256=GV9aMThwP_4oNCtvEC2ec3qUYutgWeAzklro_0m4WJQ,91
accelerate-1.6.0.dist-info/entry_points.txt,sha256=Vpy8gUGfZ-1VnM2229fb8CpJNLBdMH_wtJ9PQ7b_2tQ,296
accelerate-1.6.0.dist-info/top_level.txt,sha256=esVfdxTidsjQ90zsN_rPpjLFJ4ijRlx4mnLrG09hlt4,11
accelerate/__init__.py,sha256=r3I-pArsQK9ZrH3XgnjeCoXo4l-DEFOWQhjj3BguTZc,1504
accelerate/__pycache__/__init__.cpython-311.pyc,,
accelerate/__pycache__/accelerator.cpython-311.pyc,,
accelerate/__pycache__/big_modeling.cpython-311.pyc,,
accelerate/__pycache__/checkpointing.cpython-311.pyc,,
accelerate/__pycache__/data_loader.cpython-311.pyc,,
accelerate/__pycache__/hooks.cpython-311.pyc,,
accelerate/__pycache__/inference.cpython-311.pyc,,
accelerate/__pycache__/launchers.cpython-311.pyc,,
accelerate/__pycache__/local_sgd.cpython-311.pyc,,
accelerate/__pycache__/logging.cpython-311.pyc,,
accelerate/__pycache__/memory_utils.cpython-311.pyc,,
accelerate/__pycache__/optimizer.cpython-311.pyc,,
accelerate/__pycache__/scheduler.cpython-311.pyc,,
accelerate/__pycache__/state.cpython-311.pyc,,
accelerate/__pycache__/tracking.cpython-311.pyc,,
accelerate/accelerator.py,sha256=G952noNHGPrl-poK6qAj1OY32kGjmN5S13v8zy7H63E,173175
accelerate/big_modeling.py,sha256=IMiAtiuZQpwSyk2jQsoYC2uWzfRUSpCg7FiThSvjfKw,29702
accelerate/checkpointing.py,sha256=BaDOrpQzRI2U1BvN2vK4lepTRNNqbxGd4QPa1zOShoc,13612
accelerate/commands/__init__.py,sha256=m1PPTDT4ziIAvM0-FDSgIMIZ69Konn126s6LwuzH6v8,606
accelerate/commands/__pycache__/__init__.cpython-311.pyc,,
accelerate/commands/__pycache__/accelerate_cli.cpython-311.pyc,,
accelerate/commands/__pycache__/env.cpython-311.pyc,,
accelerate/commands/__pycache__/estimate.cpython-311.pyc,,
accelerate/commands/__pycache__/launch.cpython-311.pyc,,
accelerate/commands/__pycache__/merge.cpython-311.pyc,,
accelerate/commands/__pycache__/test.cpython-311.pyc,,
accelerate/commands/__pycache__/to_fsdp2.cpython-311.pyc,,
accelerate/commands/__pycache__/tpu.cpython-311.pyc,,
accelerate/commands/__pycache__/utils.cpython-311.pyc,,
accelerate/commands/accelerate_cli.py,sha256=SkwFad6Z1ZsGjtm7TiXFq8je-akshp_0WxX_6rGSBw8,1972
accelerate/commands/config/__init__.py,sha256=iJK8dgj3pc5Vdr1E7UuGoFu-BlybyXLxYDoTg9gXngE,1645
accelerate/commands/config/__pycache__/__init__.cpython-311.pyc,,
accelerate/commands/config/__pycache__/cluster.cpython-311.pyc,,
accelerate/commands/config/__pycache__/config.cpython-311.pyc,,
accelerate/commands/config/__pycache__/config_args.cpython-311.pyc,,
accelerate/commands/config/__pycache__/config_utils.cpython-311.pyc,,
accelerate/commands/config/__pycache__/default.cpython-311.pyc,,
accelerate/commands/config/__pycache__/sagemaker.cpython-311.pyc,,
accelerate/commands/config/__pycache__/update.cpython-311.pyc,,
accelerate/commands/config/cluster.py,sha256=w0L3zTyZp4sjDpCrM3NxOjxZ0kyPJZmzi06pFZmbM2c,37472
accelerate/commands/config/config.py,sha256=FuRlQvOjgATEtyqOSsGD-KEtOCvACOHjs2C-krrtldk,3035
accelerate/commands/config/config_args.py,sha256=xn6M8iJnlFycosDlbM0BE86r9RxfdDwHtIlk-UUq7UM,10082
accelerate/commands/config/config_utils.py,sha256=mdvZE9fpllfD8S4Blhqk3nLqQ5m14WJ0jQ1yh768H10,3177
accelerate/commands/config/default.py,sha256=sPgQVt_0zk68KlupQFqt8B6JUoPMFPxXmXr7xFM-EN8,6212
accelerate/commands/config/sagemaker.py,sha256=GjHE2-h4tRr1P_PFtMF3miiAtJlzkbHbMb6kFXqn8eo,10341
accelerate/commands/config/update.py,sha256=NXW1J7GkUHpg71QlIXsmMB_0z8S8IZo2FWax5POwrhc,2395
accelerate/commands/env.py,sha256=-B3FPX4S705A-P_tyLKm_JzGpz-TeKqFNPdNWDAdGIM,4156
accelerate/commands/estimate.py,sha256=Qduq4xudVyIede37BMEe1rNhXf-rfW-MHV2KtwxdfEA,12585
accelerate/commands/launch.py,sha256=7DI42Uw4kf_peOpY5TUA1V2yz7cuSO3cYnLgiI5G1Vs,47496
accelerate/commands/menu/__init__.py,sha256=uqSlBM0TFHBwzdv3p3SXfpAk1lZFp4h1a7mbBdscPHs,645
accelerate/commands/menu/__pycache__/__init__.cpython-311.pyc,,
accelerate/commands/menu/__pycache__/cursor.cpython-311.pyc,,
accelerate/commands/menu/__pycache__/helpers.cpython-311.pyc,,
accelerate/commands/menu/__pycache__/input.cpython-311.pyc,,
accelerate/commands/menu/__pycache__/keymap.cpython-311.pyc,,
accelerate/commands/menu/__pycache__/selection_menu.cpython-311.pyc,,
accelerate/commands/menu/cursor.py,sha256=-lmpJVAzvNc0c3EOtSuLoKB59zqylVCbYyWLPnrOmvQ,2028
accelerate/commands/menu/helpers.py,sha256=KrSB5fJjH4MUEUAQJ6bYaN16AYcnl9UalDrPD3DYeeg,1483
accelerate/commands/menu/input.py,sha256=T8Mdd-Y_OURgqfDV9qZh4Wf6hmT22AneNtJzj4JA1Rk,2512
accelerate/commands/menu/keymap.py,sha256=eXj-suyYs1m5dEHoUKN4mKAMLc8DWHnwhP6G6JSU0jQ,4086
accelerate/commands/menu/selection_menu.py,sha256=bxy-DHaKKC6SCToOlMBv5_z0MdUzylEg6Sio9OuV3GM,4921
accelerate/commands/merge.py,sha256=quDKckN3vKn9nsGjdwfoojnfTMFdKRRUkY1DYuuNNmc,2388
accelerate/commands/test.py,sha256=YrPYEaAACOGZ6btn2MV6NbMSEdBUcMWADLbQWaZSHtk,2149
accelerate/commands/to_fsdp2.py,sha256=gfbhoUT4qFB3LVDMNmckElgLG0yWm8aj_aofszeiJmM,5991
accelerate/commands/tpu.py,sha256=KyxDP7IuveidZrbW4rx2s8Ku3o_ptI6tzwr_R7ck0os,5548
accelerate/commands/utils.py,sha256=aT8xUCe2pCkFII7yZxcfaohEjgBAzMUM7WiD4UuWSOY,4150
accelerate/data_loader.py,sha256=yArisKhfuIJzDD7vuOgZAqEJNUC8tgl2L8ay92rgtfY,64551
accelerate/hooks.py,sha256=lYtYSIqEQnZOImgj2UMTngQPkcQDEHS2klwak1oHD6w,32248
accelerate/inference.py,sha256=NLANdzXm5PwmDWbPYkFmoRoQSLLvuhfvIG33xfpapT0,7668
accelerate/launchers.py,sha256=QIqUVkDc-oTmWf00L8kas7u2RBEwOYoRi8M2Our0DAs,13721
accelerate/local_sgd.py,sha256=aCj_yqXK_FhhZRWEpzXIkgXBERH6fC3HyrC3nsOj1uA,4160
accelerate/logging.py,sha256=4XcgY_BV7Qn_enh2tZ-8fNtuaE_3n-LsYJbgwhRx_PI,5042
accelerate/memory_utils.py,sha256=3R5LoeHl6GgTZ-IMPrDZMdaEehWarGdPqODushb-6pg,862
accelerate/optimizer.py,sha256=QfgCkQ5dA-fLSi_Z7CBPRCObXA1rL9zxHg4tyKCEg2A,8113
accelerate/scheduler.py,sha256=des_4M_Tt1W8gCYZZbLla0GHBEgJY3Wx2EGBQPTzeiY,4238
accelerate/state.py,sha256=YYpuPqXeNjz5_Y71h0zmCu13cBuDmQ8lw6fAmoSWUFk,55457
accelerate/test_utils/__init__.py,sha256=8xikmLMAM6_6CwVF6tsdsv4XzgWkHAk2tZBdV9DxIH8,1749
accelerate/test_utils/__pycache__/__init__.cpython-311.pyc,,
accelerate/test_utils/__pycache__/examples.cpython-311.pyc,,
accelerate/test_utils/__pycache__/testing.cpython-311.pyc,,
accelerate/test_utils/__pycache__/training.cpython-311.pyc,,
accelerate/test_utils/examples.py,sha256=IN4n2lxA95hexE2rojsyyjhpXLbXnbmjTzd8UTws5_4,7257
accelerate/test_utils/scripts/__init__.py,sha256=m1PPTDT4ziIAvM0-FDSgIMIZ69Konn126s6LwuzH6v8,606
accelerate/test_utils/scripts/__pycache__/__init__.cpython-311.pyc,,
accelerate/test_utils/scripts/__pycache__/test_cli.cpython-311.pyc,,
accelerate/test_utils/scripts/__pycache__/test_ddp_comm_hook.cpython-311.pyc,,
accelerate/test_utils/scripts/__pycache__/test_distributed_data_loop.cpython-311.pyc,,
accelerate/test_utils/scripts/__pycache__/test_merge_weights.cpython-311.pyc,,
accelerate/test_utils/scripts/__pycache__/test_notebook.cpython-311.pyc,,
accelerate/test_utils/scripts/__pycache__/test_ops.cpython-311.pyc,,
accelerate/test_utils/scripts/__pycache__/test_script.cpython-311.pyc,,
accelerate/test_utils/scripts/__pycache__/test_sync.cpython-311.pyc,,
accelerate/test_utils/scripts/external_deps/__init__.py,sha256=m1PPTDT4ziIAvM0-FDSgIMIZ69Konn126s6LwuzH6v8,606
accelerate/test_utils/scripts/external_deps/__pycache__/__init__.cpython-311.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_checkpointing.cpython-311.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_ds_multiple_model.cpython-311.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_metrics.cpython-311.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_peak_memory_usage.cpython-311.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_performance.cpython-311.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_pippy.cpython-311.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_zero3_integration.cpython-311.pyc,,
accelerate/test_utils/scripts/external_deps/test_checkpointing.py,sha256=XHaNRmnrARd1izXFjWGi5UjYGas-4vqayW51jAHBPCA,10699
accelerate/test_utils/scripts/external_deps/test_ds_multiple_model.py,sha256=Cg4-h0B4UcOQ5CxXjIdrsPVR5fFsWCv24DqZGjXEwW8,13790
accelerate/test_utils/scripts/external_deps/test_metrics.py,sha256=Ev2XKaiwmznoxKujskAAuISGChW646MOiyf0CXEPb9Y,12168
accelerate/test_utils/scripts/external_deps/test_peak_memory_usage.py,sha256=9Yn9Rc7d-yWr1fU0RagASPG5l8vrKeHVYbuYABbA-fU,12498
accelerate/test_utils/scripts/external_deps/test_performance.py,sha256=Di6LT19bCBLlWmCBSu_jjdqR2EqngXpvUOGDBx8GfZE,10432
accelerate/test_utils/scripts/external_deps/test_pippy.py,sha256=ocZntbmAduln2ma4LeEA9o-S8hla3YXCJ_A8hEcWHgs,4762
accelerate/test_utils/scripts/external_deps/test_zero3_integration.py,sha256=P9alBOHZ9Lfqs5LoRP7bCbXl-tnsNrBkvJZGseibBeA,1665
accelerate/test_utils/scripts/test_cli.py,sha256=qfk1aYFtdvYFCYPkl05602SNGvk08QTv0xZVVcFVtzM,833
accelerate/test_utils/scripts/test_ddp_comm_hook.py,sha256=k_-2MBjLKNdMGIcneTbuGd84K05Wp1GEQX6DUVF9UBw,3566
accelerate/test_utils/scripts/test_distributed_data_loop.py,sha256=RUWTwd7DIpr2fl7JtKOsvTjMiJioTxO8FdSr2Lw_5uI,15137
accelerate/test_utils/scripts/test_merge_weights.py,sha256=dssMnAoZt291vNLbPhPOTQUooh0leg_0erQh0uZH6aU,6125
accelerate/test_utils/scripts/test_notebook.py,sha256=qfIy3IvH74-kGn8nadBn_k7qrviqvsxy5ijsnUhuY6o,3894
accelerate/test_utils/scripts/test_ops.py,sha256=Bcs-h8EMJwULTfbizlFN5qkv3JraWEpoSZWMn-HswiI,6265
accelerate/test_utils/scripts/test_script.py,sha256=8-53hIVQXD28HQT4h2Ijy6yGCHfTWDAf1-HOi4UtDng,34219
accelerate/test_utils/scripts/test_sync.py,sha256=PDe8sYZLCL2LKjj_L9b-Bh2BjAjeii9EZ8sZNfuYx5s,18817
accelerate/test_utils/testing.py,sha256=x9RK70VgAMyHlo5xut7P85j-9kdAnlfQe_4jwSPpMv4,27807
accelerate/test_utils/training.py,sha256=jO5YEIr34jAcnJ_9WNp_x3zuHzSam_I6IgMvmcGm7yI,6456
accelerate/tracking.py,sha256=ucpsoYAT3pVXgOfwDdXf6qTugY2-tk-EINvZtfmRitM,42756
accelerate/utils/__init__.py,sha256=wjpXyvFxS-ed3Stwm_IHIlBmsmP7KRyAljQ_Qss-OWw,7802
accelerate/utils/__pycache__/__init__.cpython-311.pyc,,
accelerate/utils/__pycache__/ao.cpython-311.pyc,,
accelerate/utils/__pycache__/bnb.cpython-311.pyc,,
accelerate/utils/__pycache__/constants.cpython-311.pyc,,
accelerate/utils/__pycache__/dataclasses.cpython-311.pyc,,
accelerate/utils/__pycache__/deepspeed.cpython-311.pyc,,
accelerate/utils/__pycache__/environment.cpython-311.pyc,,
accelerate/utils/__pycache__/fsdp_utils.cpython-311.pyc,,
accelerate/utils/__pycache__/imports.cpython-311.pyc,,
accelerate/utils/__pycache__/launch.cpython-311.pyc,,
accelerate/utils/__pycache__/megatron_lm.cpython-311.pyc,,
accelerate/utils/__pycache__/memory.cpython-311.pyc,,
accelerate/utils/__pycache__/modeling.cpython-311.pyc,,
accelerate/utils/__pycache__/offload.cpython-311.pyc,,
accelerate/utils/__pycache__/operations.cpython-311.pyc,,
accelerate/utils/__pycache__/other.cpython-311.pyc,,
accelerate/utils/__pycache__/random.cpython-311.pyc,,
accelerate/utils/__pycache__/rich.cpython-311.pyc,,
accelerate/utils/__pycache__/torch_xla.cpython-311.pyc,,
accelerate/utils/__pycache__/tqdm.cpython-311.pyc,,
accelerate/utils/__pycache__/transformer_engine.cpython-311.pyc,,
accelerate/utils/__pycache__/versions.cpython-311.pyc,,
accelerate/utils/ao.py,sha256=koMiji7AG1kJMRMkJnwSnpuycfx4lPY3CNnpNx2ZqzM,4736
accelerate/utils/bnb.py,sha256=KCbg6LUt4eXvPHVnKh7rSVcPwDnzxY_Ii7yYmK5bNGw,20737
accelerate/utils/constants.py,sha256=hc24V0pgxWdBQwS6SXxDKwuIni2pCnzdfvMOX1XI9Os,3264
accelerate/utils/dataclasses.py,sha256=E7CnCbfskpzxzSorst95Via_XE39t0NP_UGYgJUris0,131486
accelerate/utils/deepspeed.py,sha256=QYIXv5LwHXw7wBFFo-7a0t86MbwNAfieJkkBaLGA6wI,14064
accelerate/utils/environment.py,sha256=h0zacbBkAp9szltTf5-aTr5NcbVsQp7wl6DFWp8XNuI,15257
accelerate/utils/fsdp_utils.py,sha256=Q2tc9EakwBjuYlyXvQrBLV97r6cdReRft6KeS1P_Vb4,28938
accelerate/utils/imports.py,sha256=YI1ebPJAuxarclENTfzvDPPGf6jeEKnVQ42taFPuqh0,16759
accelerate/utils/launch.py,sha256=nN4ykAtnEL3oITLTejABltdpS3OivcE2COmX-BnWuY4,31195
accelerate/utils/megatron_lm.py,sha256=FnIF-niZjvdMk9ymafZWEPjDho_Q_P98C69qc9g5r_E,58059
accelerate/utils/memory.py,sha256=lDHqW7Ue_CPmw_DWgNxX_B3HY71_srAFdgR10XiVRSM,6960
accelerate/utils/modeling.py,sha256=_xSTiH7zSsffZULSTJuzcDK6IaWImEMOcbq1xqeI7GY,92319
accelerate/utils/offload.py,sha256=VFaL8oSJzqZ_47VuUQ69xZi9bF2heRSFoOSnnOxbGXc,7825
accelerate/utils/operations.py,sha256=VWPYvtrO4UGX5JmisanXzLLUbhAeL8kQk0yYc66bQ2M,31055
accelerate/utils/other.py,sha256=iiLZcKEAlK2Sj_wt03gAEGKrk7_NZFwbmy9cgEppRPw,13231
accelerate/utils/random.py,sha256=Xv_ZJm9eaC2Q7rgZy9OpOunKuTingMiDQCH00qhNVxE,6220
accelerate/utils/rich.py,sha256=8JZX_uGMQX-BufdXxJpdne7BWd1KyLHSgbiGxrDMYr8,847
accelerate/utils/torch_xla.py,sha256=Pq1tuqN0X_pWDVza6YgjfO45uoJdoRVRForLeLQzFus,1908
accelerate/utils/tqdm.py,sha256=k8e9JnieTEQHCCNBaiBys7hPxWlEbyRASdIma-qy_X8,1657
accelerate/utils/transformer_engine.py,sha256=498Y3z2BkbybYLtBiuF_TJgt8Iii943s4wgRAV8FDC4,6372
accelerate/utils/versions.py,sha256=UgmcbjBm--6CIx1ZamSAMjAK_B_2l48LbeaNygqej8M,2149
